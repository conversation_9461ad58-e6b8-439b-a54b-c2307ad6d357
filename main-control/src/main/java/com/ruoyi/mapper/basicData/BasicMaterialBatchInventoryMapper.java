package com.ruoyi.mapper.basicData;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.domain.basicData.BasicMaterialBatchInventory;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.vo.warehouse.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BasicMaterialBatchInventoryMapper extends BaseMapper<BasicMaterialBatchInventory> {
    int updateByPrimaryKeySelective(BasicMaterialBatchInventory record);

    List<BasicMaterialDetailDto> queryBasicMaterialWarehouseInfo(QueryParamVO queryParamVO);

    List<BasicMaterialBatchInventoryDto> queryBasicMaterialInventory(QueryParamVO queryParamVO);

    List<MaterialContainerInfoDto> queryContainerMaterialInfo(@Param("queryParamVO") QueryParamVO queryParamVO, @Param("materialCodes") List<String> materialCodes);

    List<BasicMaterialBatchInventory> selectByContainerCode(@Param("containerCode") String containerCode);

    Integer qryCountByMaterialCode(@Param("materialCode")String materialCode);

    MaterialInventoryDto queryInventoryByCode(@Param("material_code") String material_code);

    List<BasicMaterialBatchInventory> getAvailBatchByMaterialCode(@Param("materialCode") String materialCode);

    List<BasicMaterialNumInfo> queryMaterialNumDate(QueryParamVO queryParamVO);

    List<BasicMaterialAlertInfo> queryMaterialAlertInfo(QueryParamVO queryParamVO);

    List<MaterialAlertInfo> queryMaterialExpirationAlert(QueryParamVO queryParamVO);

    Integer qryNumByMaterialCode(String materialCode);

    /**
     * 根据物料编码列表批量查询物料库存信息
     * @param materialCodes 物料编码列表
     * @return 物料库存信息列表
     */
    List<BasicMaterialNumInfo> queryMaterialNumInfoByMaterialCodes(@Param("materialCodes") List<String> materialCodes);

    /**
     * 优化查询：一次性获取物料信息及其可用容器列表（包含位置信息）
     * @param queryParamVO 查询参数
     * @return 物料及容器信息列表
     */
    List<MaterialContainerInfoDto> queryMaterialWithContainersOptimized(QueryParamVO queryParamVO);

    /**
     * 根据物料编码列表优化查询：一次性获取物料信息及其可用容器列表（包含位置信息）
     * @param materialCodes 物料编码列表
     * @return 物料及容器信息列表
     */
    List<MaterialContainerInfoDto> queryMaterialWithContainersByCodesOptimized(@Param("materialCodes") List<String> materialCodes);
}