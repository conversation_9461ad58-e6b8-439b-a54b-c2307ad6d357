package com.ruoyi.service.basicData;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.LocationUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.domain.basicData.BasicMaterialBatchInventory;
import com.ruoyi.domain.basicData.RecordInoutDetail;
import com.ruoyi.mapper.basicData.BasicMaterialBatchInventoryMapper;
import com.ruoyi.mapper.basicData.BasicWarehouseContainerMapper;
import com.ruoyi.service.work.LkSystemService;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.InterConstant;
import com.ruoyi.vo.warehouse.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class BasicMaterialBatchInventoryService extends ServiceImpl<BasicMaterialBatchInventoryMapper, BasicMaterialBatchInventory> {

    private static final Logger logger = LoggerFactory.getLogger(BasicMaterialBatchInventoryService.class);

    @Resource
    BasicMaterialBatchInventoryMapper basicMaterialBatchInventoryMapper;
    @Resource
    BasicWarehouseContainerMapper basicWarehouseContainerMapper;
    @Resource
    LkSystemService lkSystemService;


    /**
     * 查询物料仓库批次信息
     */
    public List<BasicMaterialBatchInventoryDto> queryBasicMaterialInventory(QueryParamVO queryParamVO) {
        List<BasicMaterialBatchInventoryDto> basicMaterialBatchInventoryDtos = basicMaterialBatchInventoryMapper.queryBasicMaterialInventory(queryParamVO);

        // 批量补充位置信息
        if (!basicMaterialBatchInventoryDtos.isEmpty()) {
            // 收集所有容器编码
            List<String> containerCodes = basicMaterialBatchInventoryDtos.stream()
                    .map(BasicMaterialBatchInventoryDto::getContainerCode)
                    .filter(StringUtils::isNotEmpty)
                    .distinct()
                    .collect(Collectors.toList());

            if (!containerCodes.isEmpty()) {
                // 批量查询位置信息
                List<ContainerLocationInfoDto> locationInfos = basicWarehouseContainerMapper.queryContainerLocationInfoBatch(containerCodes);
                Map<String, ContainerLocationInfoDto> locationMap = locationInfos.stream()
                        .collect(Collectors.toMap(ContainerLocationInfoDto::getContainerCode, Function.identity()));

                // 为每个DTO设置位置信息
                for (BasicMaterialBatchInventoryDto dto : basicMaterialBatchInventoryDtos) {
                    ContainerLocationInfoDto locationInfo = locationMap.get(dto.getContainerCode());
                    if (locationInfo != null) {
                        BeanUtils.copyProperties(locationInfo, dto);
                    }
                }
            }
        }

        return basicMaterialBatchInventoryDtos;
    }

    /**
     * 查询物料仓库信息
     */
    public List<BasicMaterialDetailDto> queryBasicMaterialWarehouseInfo(QueryParamVO queryParamVO) {
        return basicMaterialBatchInventoryMapper.queryBasicMaterialWarehouseInfo(queryParamVO);
    }

    public boolean freezeMaterial(String boundIndex, String inventoryId, Integer freezeNum) {
        if (inventoryId == null || inventoryId.trim().isEmpty()) {
            logger.error("冻结物料失败：库存ID不能为空，单据号：{}",boundIndex);
            return false;
        }
        if (freezeNum == null || freezeNum <= 0) {
            logger.error("冻结物料失败：冻结数量必须大于0，当前值: {}，单据号：{}", freezeNum,boundIndex);
            return false;
        }
        BasicMaterialBatchInventory batchInventory = basicMaterialBatchInventoryMapper.selectById(inventoryId);
        if (batchInventory == null) {
            logger.error("冻结物料失败：库存记录不存在，ID: {}，单据号：{}", inventoryId,boundIndex);
            return false;
        }
        // 检查可用数量是否足够冻结
        if (batchInventory.getAvailNum() < freezeNum) {
            logger.error("单据{}，冻结物料失败：可用数量不足，当前可用: {}，需要冻结: {}", boundIndex,batchInventory.getAvailNum(), freezeNum);
            return false;
        }
        Integer newFreezeNum = batchInventory.getFreezeNum() + freezeNum;
        batchInventory.setFreezeNum(newFreezeNum);
        batchInventory.setAvailNum(batchInventory.getMaterialNum() - newFreezeNum);
        int uptNum = basicMaterialBatchInventoryMapper.updateById(batchInventory);
        return uptNum >= 1;
    }

    /**
     * 冻结物料 指定批次库存记录
     */
    @Transactional
    public boolean freezeMaterial(String inventoryId, Integer freezeNum) {
        return freezeMaterial(null,inventoryId,freezeNum);
    }

    /**
     * 解冻物料 通过出入库记录
     */
    public void unfreezeMaterial(String inventoryId, Integer unfreeze_Num) {
        if (inventoryId == null || inventoryId.trim().isEmpty()) {
            logger.error("解冻物料失败：库存ID不能为空");
            return;
        }
        if (unfreeze_Num == null || unfreeze_Num <= 0) {
            logger.error("解冻物料失败：解冻数量必须大于0，当前值: {}", unfreeze_Num);
            return;
        }

        BasicMaterialBatchInventory batchInventory = basicMaterialBatchInventoryMapper.selectById(inventoryId);
        if (batchInventory == null) {
            logger.error("解冻物料失败：库存记录不存在，ID: {}", inventoryId);
            return;
        }

        // 检查冻结数量是否足够解冻
        if (batchInventory.getFreezeNum() < unfreeze_Num) {
            logger.error("解冻物料失败：冻结数量不足，当前冻结: {}，需要解冻: {}", batchInventory.getFreezeNum(), unfreeze_Num);
            return;
        }

        batchInventory.setFreezeNum(batchInventory.getFreezeNum() - unfreeze_Num);
        batchInventory.setAvailNum(batchInventory.getAvailNum() + unfreeze_Num);
        basicMaterialBatchInventoryMapper.updateById(batchInventory);
    }

    /**
     * 批量解冻物料
     */
    @Transactional
    public void unfreezeMaterialBatch(List<RecordInoutDetail> recordInoutDetails) {
        if (recordInoutDetails == null || recordInoutDetails.isEmpty()) {
            logger.error("批量解冻物料失败：出入库详情列表不能为空");
            return;
        }

        for (RecordInoutDetail recordInoutDetail : recordInoutDetails) {
            if (recordInoutDetail.getInventoryId() == null || recordInoutDetail.getInventoryId().trim().isEmpty()) {
                logger.error("批量解冻物料失败：库存ID不能为空");
                continue;
            }
            if (recordInoutDetail.getMaterialNum() == null || recordInoutDetail.getMaterialNum() <= 0) {
                logger.error("批量解冻物料失败：解冻数量必须大于0，当前值: {}", recordInoutDetail.getMaterialNum());
                continue;
            }

            BasicMaterialBatchInventory batchInventory = basicMaterialBatchInventoryMapper.selectById(recordInoutDetail.getInventoryId());
            if (batchInventory == null) {
                logger.error("批量解冻物料失败：库存记录不存在，ID: {}", recordInoutDetail.getInventoryId());
                continue;
            }

            // 检查冻结数量是否足够解冻
            if (batchInventory.getFreezeNum() < recordInoutDetail.getMaterialNum()) {
                logger.error("批量解冻物料失败：冻结数量不足，当前冻结: {}，需要解冻: {}", batchInventory.getFreezeNum(), recordInoutDetail.getMaterialNum());
                continue;
            }

            // 更新冻结数量和可用数量
            batchInventory.setFreezeNum(batchInventory.getFreezeNum() - recordInoutDetail.getMaterialNum());
            batchInventory.setAvailNum(batchInventory.getAvailNum() + recordInoutDetail.getMaterialNum());
            basicMaterialBatchInventoryMapper.updateById(batchInventory);
        }
    }


    /**
     * 出库修改物料库存数量 通过出入库详情
     */
    public ResponseResult updateMaterialNum(RecordInoutDetail recordInoutDetail) {
        if (recordInoutDetail == null) {
            return ResponseResult.getErrorResult("出入库详情不能为空");
        }
        if (recordInoutDetail.getInventoryId() == null || recordInoutDetail.getInventoryId().trim().isEmpty()) {
            return ResponseResult.getErrorResult("库存ID不能为空");
        }
        if (recordInoutDetail.getMaterialNum() == null || recordInoutDetail.getMaterialNum() <= 0) {
            return ResponseResult.getErrorResult("出库数量必须大于0");
        }

        BasicMaterialBatchInventory batchInventory = basicMaterialBatchInventoryMapper.selectById(recordInoutDetail.getInventoryId());
        if (batchInventory == null) {
            return ResponseResult.getErrorResult("库存记录不存在，ID: " + recordInoutDetail.getInventoryId());
        }

        if (batchInventory.getMaterialNum() - recordInoutDetail.getMaterialNum() < 0) {
            return ResponseResult.getErrorResult("物料总数量不足，当前总数量: " + batchInventory.getMaterialNum() + "，需要出库: " + recordInoutDetail.getMaterialNum());
        }

        if (batchInventory.getFreezeNum() - recordInoutDetail.getMaterialNum() < 0) {
            return ResponseResult.getErrorResult("冻结数量不足，当前冻结: " + batchInventory.getFreezeNum() + "，需要出库: " + recordInoutDetail.getMaterialNum());
        }

        batchInventory.setMaterialNum(batchInventory.getMaterialNum() - recordInoutDetail.getMaterialNum());
        batchInventory.setFreezeNum(batchInventory.getFreezeNum() - recordInoutDetail.getMaterialNum());
        batchInventory.setAvailNum(batchInventory.getMaterialNum() - batchInventory.getFreezeNum());
        basicMaterialBatchInventoryMapper.updateById(batchInventory);

        //库存数量为0删除该批次库存
        if (batchInventory.getMaterialNum() == 0 && batchInventory.getAvailNum() == 0){
            basicMaterialBatchInventoryMapper.deleteById(batchInventory.getId());
        }
        return ResponseResult.getSuccessResult();
    }

    /**
     * 出库修改物料库存数量 指定批次库存记录以及出库数量
     */
    public ResponseResult updateMaterialNum(BasicMaterialBatchInventory batchInventory, Integer material_num) {
        if (batchInventory == null) {
            return ResponseResult.getErrorResult("库存记录不能为空");
        }
        if (material_num == null || material_num <= 0) {
            return ResponseResult.getErrorResult("出库数量必须大于0");
        }

        if (batchInventory.getMaterialNum() - material_num < 0) {
            return ResponseResult.getErrorResult("物料总数量不足，当前总数量: " + batchInventory.getMaterialNum() + "，需要出库: " + material_num);
        }

        if (batchInventory.getFreezeNum() - material_num < 0) {
            return ResponseResult.getErrorResult("冻结数量不足，当前冻结: " + batchInventory.getFreezeNum() + "，需要出库: " + material_num);
        }

        batchInventory.setMaterialNum(batchInventory.getMaterialNum() - material_num);
        batchInventory.setFreezeNum(batchInventory.getFreezeNum() - material_num);
        batchInventory.setAvailNum(batchInventory.getMaterialNum() - batchInventory.getFreezeNum());
        basicMaterialBatchInventoryMapper.updateById(batchInventory);

        //库存数量为0删除该批次库存
        if (batchInventory.getMaterialNum() == 0 && batchInventory.getAvailNum() == 0){
            basicMaterialBatchInventoryMapper.deleteById(batchInventory.getId());
        }
        return ResponseResult.getSuccessResult();
    }

    /**
     * 通过容器编码、物料编码、批次、生产日期查询物料批次库存记录·
     *
     * @param recordInoutDetail 物料出入库记录信息
     * @return 物料批次库存记录
     */
    public BasicMaterialBatchInventory getInventoryByRecordInoutDetail(RecordInoutDetail recordInoutDetail) {
        LambdaQueryWrapper<BasicMaterialBatchInventory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BasicMaterialBatchInventory::getContainerCode, recordInoutDetail.getContainerCode());
        wrapper.eq(BasicMaterialBatchInventory::getMaterialCode, recordInoutDetail.getMaterialCode());
        wrapper.eq(BasicMaterialBatchInventory::getBatch, recordInoutDetail.getBatch());
        wrapper.eq(BasicMaterialBatchInventory::getProduceDate, recordInoutDetail.getProduceDate());
        return this.getOne(wrapper);
    }

    /**
     * 通过容器编码和物料编码查询物料批次库存记录
     *
     * @param containerCode 容器编码
     * @param materialCode 物料编码
     * @return 物料批次库存记录
     */
    public BasicMaterialBatchInventory getInventoryByContainerAndMaterial(String containerCode, String materialCode) {
        LambdaQueryWrapper<BasicMaterialBatchInventory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BasicMaterialBatchInventory::getContainerCode, containerCode);
        wrapper.eq(BasicMaterialBatchInventory::getMaterialCode, materialCode);
        // 如果有多个批次，取第一个（按入库时间排序）
        wrapper.orderByAsc(BasicMaterialBatchInventory::getInDate);
        wrapper.last("LIMIT 1");
        return this.getOne(wrapper);
    }

    /**
     * 通过容器编码、物料编码、批次、生产日期查询物料批次库存记录·
     *
     * @return 物料批次库存记录
     */
    public BasicMaterialBatchInventory getMaterialBatchInventory(String container_code, String batch, String material_code, Date produce_date) {
        LambdaQueryWrapper<BasicMaterialBatchInventory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BasicMaterialBatchInventory::getContainerCode, container_code);
        wrapper.eq(BasicMaterialBatchInventory::getMaterialCode, material_code);
        wrapper.eq(BasicMaterialBatchInventory::getBatch, batch);
        if (produce_date == null) {
            wrapper.isNull(BasicMaterialBatchInventory::getProduceDate);
        } else {
            wrapper.eq(BasicMaterialBatchInventory::getProduceDate, produce_date);
        }
        wrapper.last("limit 1");
        return this.getOne(wrapper);
    }

    /**
     * 新增物料批次库存记录
     *
     * @param recordInoutDetails
     */
    public void insertOrUpdateByRecordInoutDetailList(List<RecordInoutDetail> recordInoutDetails) {
        Date currentDate = new Date();
        List<BasicMaterialBatchInventory> batchInventories = new ArrayList<>();
        for (RecordInoutDetail recordInoutDetail : recordInoutDetails) {
            BasicMaterialBatchInventory existingBatchInventory = basicMaterialBatchInventoryMapper.selectById(recordInoutDetail.getInventoryId());
            //如果不存在，则创建新记录
            if (existingBatchInventory == null) {
                BasicMaterialBatchInventory batchInventory = new BasicMaterialBatchInventory();
                BeanUtils.copyProperties(recordInoutDetail, batchInventory);
                batchInventory.setId(UUID.randomUUID().toString());
                batchInventory.setInDate(currentDate);
                batchInventory.setCreateTime(new Date());
                batchInventory.setFreezeNum(0);
                batchInventory.setAvailNum(recordInoutDetail.getMaterialNum());
                batchInventories.add(batchInventory);
            } else {
                Integer newAvailNum = existingBatchInventory.getAvailNum() + recordInoutDetail.getMaterialNum();
                existingBatchInventory.setAvailNum(newAvailNum);
                existingBatchInventory.setMaterialNum(existingBatchInventory.getMaterialNum() + recordInoutDetail.getMaterialNum());
                batchInventories.add(existingBatchInventory);
            }
        }
        // 批量保存或更新
        saveOrUpdateBatch(batchInventories);
    }


    public List<MaterialContainerInfoDto> queryContainerMaterialInfo(QueryParamVO queryParamVO) {
        return basicMaterialBatchInventoryMapper.queryContainerMaterialInfo(queryParamVO,null);
    }

    /**
     * 通过主键ID修改
     */
    @Transactional
    public boolean updateByPrimaryKeySelective(BasicMaterialBatchInventory record) {
        return basicMaterialBatchInventoryMapper.updateByPrimaryKeySelective(record) > 0;
    }

    /**
     * 查询物料库存数量
     */
    public List<BasicMaterialNumInfo> queryMaterialNumDate(QueryParamVO queryParamVO) {
        //查询库存数量
        return basicMaterialBatchInventoryMapper.queryMaterialNumDate(queryParamVO);
    }

    /**
     * 查询物料库存数量（单个）
     */
    public Integer queryMaterialNumByCode(String materialCode) {
        //查询库存数量
        Integer materialNum = basicMaterialBatchInventoryMapper.qryNumByMaterialCode(materialCode);
        Integer boxInteger = lkSystemService.inventoryStatistics(InterConstant.InterType.BOX_INVENTORY_STATISTICS, materialCode);
        Integer plateInteger = lkSystemService.inventoryStatistics(InterConstant.InterType.PLATE_INVENTORY_STATISTICS, materialCode);
        Integer profileInteger = lkSystemService.inventoryStatistics(InterConstant.InterType.PROFILE_INVENTORY_STATISTICS, materialCode);
        materialNum = materialNum + boxInteger + plateInteger + profileInteger;
        return materialNum;
    }

    /**
     * 查询物料预警信息
     */
    public List<BasicMaterialAlertInfo> queryMaterialAlertInfo(QueryParamVO queryParamVO) {
        return basicMaterialBatchInventoryMapper.queryMaterialAlertInfo(queryParamVO);
    }

    /**
     * 查询物料过期预警信息
     */
    public List<MaterialAlertInfo> queryMaterialExpirationAlert(QueryParamVO queryParamVO) {
        List<MaterialAlertInfo> materialAlertInfos = basicMaterialBatchInventoryMapper.queryMaterialExpirationAlert(queryParamVO);
        //补充位置信息
        for (MaterialAlertInfo materialAlertInfo : materialAlertInfos) {
            ContainerLocationInfoDto containerLocationInfoDto = basicWarehouseContainerMapper.queryContainerLocationInfo(materialAlertInfo.getContainerCode());
            if (containerLocationInfoDto != null) {
                materialAlertInfo.setMaterialLocation(containerLocationInfoDto.getWarehouseName() + "-" + containerLocationInfoDto.getShelfName() + "-" + containerLocationInfoDto.getLevelName() + "-" + containerLocationInfoDto.getPositionName());
            }
        }

        return materialAlertInfos;
    }

    public List<BasicMaterialBatchInventory> selectByContainerCode(String containerCode) {
        return this.basicMaterialBatchInventoryMapper.selectByContainerCode(containerCode);
    }

    /**
     * 批量查询多个物料的可用容器列表
     *
     * @param materialCodes 物料编码列表
     * @return 物料编码 -> 可用容器列表的映射
     */
    public Map<String, List<BasicMaterialBatchInventory>> getAvailableContainersByMaterials(List<String> materialCodes) {
        if (materialCodes == null || materialCodes.isEmpty()) {
            return new HashMap<>();
        }

        // 批量查询所有物料的可用容器
        LambdaQueryWrapper<BasicMaterialBatchInventory> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BasicMaterialBatchInventory::getMaterialCode, materialCodes)
               .gt(BasicMaterialBatchInventory::getAvailNum, 0)
               .orderByAsc(BasicMaterialBatchInventory::getMaterialCode)
               .orderByAsc(BasicMaterialBatchInventory::getCreateTime); // 按创建时间排序，支持FIFO

        List<BasicMaterialBatchInventory> allContainers = basicMaterialBatchInventoryMapper.selectList(wrapper);

        // 批量补充位置信息
        if (!allContainers.isEmpty()) {
            // 收集所有容器编码
            List<String> containerCodes = allContainers.stream()
                    .map(BasicMaterialBatchInventory::getContainerCode)
                    .filter(StringUtils::isNotEmpty)
                    .distinct()
                    .collect(Collectors.toList());

            if (!containerCodes.isEmpty()) {
                try {
                    // 批量查询位置信息
                    List<ContainerLocationInfoDto> locationInfos = basicWarehouseContainerMapper.queryContainerLocationInfoBatch(containerCodes);
                    Map<String, ContainerLocationInfoDto> locationMap = locationInfos.stream()
                            .collect(Collectors.toMap(ContainerLocationInfoDto::getContainerCode, Function.identity()));

                    // 为每个容器设置位置信息
                    for (BasicMaterialBatchInventory container : allContainers) {
                        ContainerLocationInfoDto locationInfo = locationMap.get(container.getContainerCode());
                        if (locationInfo != null) {
                            // 使用LocationUtils智能拼接位置信息，自动过滤空值
                            String location = LocationUtils.formatLocation(locationInfo);
                            container.setLocation(location);
                        }
                    }
                } catch (Exception e) {
                    // 如果批量查询位置信息失败，记录日志但不影响主流程
                    logger.warn("批量查询容器位置信息失败", e);
                }
            }
        }

        // 按物料编码分组
        return allContainers.stream().collect(Collectors.groupingBy(BasicMaterialBatchInventory::getMaterialCode));
    }

    /**
     * 根据物料编码查询可用容器列表
     * 用于出库单据选择容器时查询可用的库存
     *
     * @param materialCode 物料编码
     * @return 可用容器列表
     */
    public List<BasicMaterialBatchInventory> getAvailableContainersByMaterial(String materialCode) {
        LambdaQueryWrapper<BasicMaterialBatchInventory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BasicMaterialBatchInventory::getMaterialCode, materialCode)
               .gt(BasicMaterialBatchInventory::getAvailNum, 0)
               .orderByDesc(BasicMaterialBatchInventory::getCreateTime);

        List<BasicMaterialBatchInventory> containers = basicMaterialBatchInventoryMapper.selectList(wrapper);

        // 为每个容器添加位置信息
        for (BasicMaterialBatchInventory container : containers) {
            String containerCode = container.getContainerCode();
            if (StringUtils.isNotEmpty(containerCode)) {
                try {
                    ContainerLocationInfoDto locationInfo = basicWarehouseContainerMapper.queryContainerLocationInfo(containerCode);
                    if (locationInfo != null) {
                        String location = locationInfo.getWarehouseName() + "-" + locationInfo.getShelfName() +
                                        "-" + locationInfo.getLevelName() + "-" + locationInfo.getPositionName();
                        container.setLocation(location);
                    }
                } catch (Exception e) {
                    // 如果查询位置信息失败，记录日志但不影响主流程
                    logger.warn("查询容器位置信息失败，容器编码：{}", containerCode, e);
                }
            }
        }

        return containers;
    }
    /**
     * 优化查询：一次性获取物料信息及其可用容器列表（包含位置信息）
     *
     * @param queryParamVO 查询参数
     * @return 物料及容器信息列表，已包含位置信息
     */
    public List<MaterialContainerInfoDto> queryMaterialWithContainersOptimized(QueryParamVO queryParamVO) {
        List<MaterialContainerInfoDto> results = basicMaterialBatchInventoryMapper.queryMaterialWithContainersOptimized(queryParamVO);

        // 使用LocationUtils处理位置信息拼接
        for (MaterialContainerInfoDto dto : results) {
            String location = LocationUtils.formatLocation(
                dto.getWarehouseName(),
                dto.getShelfName(),
                dto.getLevelName(),
                dto.getPositionName()
            );
            dto.setLocation(location);
        }

        return results;
    }

    /**
     * 根据物料编码列表优化查询：一次性获取物料信息及其可用容器列表（包含位置信息）
     *
     * @param materialCodes 物料编码列表
     * @return 物料及容器信息列表，已包含位置信息
     */
    public List<MaterialContainerInfoDto> queryMaterialWithContainersByCodesOptimized(List<String> materialCodes) {
        if (materialCodes == null || materialCodes.isEmpty()) {
            return new ArrayList<>();
        }

        List<MaterialContainerInfoDto> results = basicMaterialBatchInventoryMapper.queryMaterialWithContainersByCodesOptimized(materialCodes);

        // 使用LocationUtils处理位置信息拼接
        for (MaterialContainerInfoDto dto : results) {
            String location = LocationUtils.formatLocation(
                dto.getWarehouseName(),
                dto.getShelfName(),
                dto.getLevelName(),
                dto.getPositionName()
            );
            dto.setLocation(location);
        }

        return results;
    }

    /**
     * 根据物料编码列表批量查询物料库存信息
     * 用于获取完整的物料信息，包括库存数量等
     *
     * @param materialCodes 物料编码列表
     * @return 物料库存信息列表
     */
    public List<BasicMaterialNumInfo> queryMaterialNumInfoByMaterialCodes(List<String> materialCodes) {
        if (materialCodes == null || materialCodes.isEmpty()) {
            return new ArrayList<>();
        }
        return basicMaterialBatchInventoryMapper.queryMaterialNumInfoByMaterialCodes(materialCodes);
    }
}
