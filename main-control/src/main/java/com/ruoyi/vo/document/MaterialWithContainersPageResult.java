package com.ruoyi.vo.document;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 物料及容器信息分页结果
 * 用于一次查询返回分页数据和总数
 * 
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MaterialWithContainersPageResult {
    
    /**
     * 分页数据
     */
    private List<MaterialWithContainersDto> data;
    
    /**
     * 总记录数
     */
    private long total;
}
